"""
共享主干网络多任务模型
架构：输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
       ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
       └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import cv2
import numpy as np
import easyocr
import os


class SharedBackboneModel(nn.Module):
    """共享主干网络多任务模型"""
    
    def __init__(self, num_classes=47):
        super(SharedBackboneModel, self).__init__()
        
        # 共享主干网络 (Backbone)
        self.backbone = nn.Sequential(
            nn.Conv2d(3, 64, 7, stride=2, padding=3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(3, stride=2, padding=1),
            
            nn.Conv2d(64, 128, 3, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.ReL<PERSON>(inplace=True),
            
            nn.Conv2d(128, 256, 3, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(256, 512, 3, stride=2, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )
        
        # 特征金字塔网络 (Neck)
        self.neck = nn.Sequential(
            nn.Conv2d(512, 256, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Upsample(scale_factor=2, mode='nearest'),
        )
        
        # 元器件检测头 (YOLO head) - 3个尺度的检测
        self.detection_head = nn.ModuleList([
            # 大目标检测 (20x20)
            nn.Sequential(
                nn.Conv2d(256, 128, 3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 3 * (num_classes + 5), 1),  # 3个anchor * (classes + x,y,w,h,conf)
            ),
            # 中目标检测 (40x40)
            nn.Sequential(
                nn.Conv2d(256, 128, 3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 3 * (num_classes + 5), 1),
            ),
            # 小目标检测 (80x80)
            nn.Sequential(
                nn.Conv2d(256, 128, 3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 3 * (num_classes + 5), 1),
            )
        ])

        # YOLO anchor boxes (3个尺度，每个尺度3个anchor)
        self.anchors = torch.tensor([
            [[10, 13], [16, 30], [33, 23]],      # 小目标
            [[30, 61], [62, 45], [59, 119]],     # 中目标
            [[116, 90], [156, 198], [373, 326]]  # 大目标
        ]).float()

        self.num_classes = num_classes
        
        # 文字检测头 (OCR detection head)
        self.text_detection_head = nn.Sequential(
            nn.Conv2d(256, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 1, 1),  # 文字/非文字二分类
            nn.Sigmoid()
        )
        
        # 文字识别头 (OCR recognition head)
        self.text_recognition_head = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(256, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, 1000),  # 常用汉字词汇表
        )
        
        # OCR引擎 - 优化配置
        try:
            self.ocr_engine = easyocr.Reader(
                ['ch_sim', 'en'],
                gpu=torch.cuda.is_available(),
                model_storage_directory='./easyocr_models',  # 指定模型存储目录
                download_enabled=True
            )
            print("✅ OCR引擎初始化成功")
        except Exception as e:
            print(f"⚠️ OCR引擎初始化失败: {e}")
            self.ocr_engine = None
        
        print("✅ 共享主干网络模型初始化完成")

    def forward(self, x):
        """
        前向传播：实现共享主干网络的多任务架构
        输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
           ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
           └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
        """
        # 1. 主干网络特征提取 (Backbone)
        backbone_features = self.backbone(x)

        # 2. 特征金字塔网络 (Neck) - 生成多尺度特征
        neck_features = self.neck(backbone_features)

        # 创建不同尺度的特征图
        # 大尺度特征 (40x40)
        large_features = neck_features
        # 中尺度特征 (20x20)
        medium_features = F.interpolate(large_features, scale_factor=0.5, mode='nearest')
        # 小尺度特征 (10x10)
        small_features = F.interpolate(medium_features, scale_factor=0.5, mode='nearest')

        # 3. 双分支输出
        # 分支1: 元器件检测头 (YOLO head) - 多尺度检测
        detection_outputs = []
        feature_maps = [small_features, medium_features, large_features]  # 从小到大

        for i, (features, detection_head) in enumerate(zip(feature_maps, self.detection_head)):
            detection_out = detection_head(features)
            # 重塑为 [batch, anchors, grid_h, grid_w, (classes + 5)]
            batch_size = detection_out.size(0)
            grid_h, grid_w = detection_out.size(2), detection_out.size(3)
            detection_out = detection_out.view(batch_size, 3, self.num_classes + 5, grid_h, grid_w)
            detection_out = detection_out.permute(0, 1, 3, 4, 2).contiguous()
            detection_outputs.append(detection_out)

        # 分支2: 文字检测+识别头 (OCR head)
        text_detection_output = self.text_detection_head(large_features)
        text_recognition_output = self.text_recognition_head(large_features)

        return {
            'detection': detection_outputs,          # YOLO多尺度检测结果
            'text_detection': text_detection_output, # 文字区域检测
            'text_recognition': text_recognition_output  # 文字内容识别
        }

    def predict(self, image_path):
        """预测方法"""
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return None
            
        # 读取和预处理图像
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image_tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
        
        # 调整到标准尺寸
        image_tensor = F.interpolate(image_tensor, size=(640, 640), mode='bilinear', align_corners=False)
        
        # 前向传播
        with torch.no_grad():
            outputs = self.forward(image_tensor)
        
        # 使用OCR引擎进行文字识别，并过滤低质量结果
        ocr_results = []
        if self.ocr_engine:
            try:
                # 优化OCR参数：提高检测精度，减少误检
                raw_ocr_results = self.ocr_engine.readtext(
                    image,
                    detail=1,           # 返回详细信息
                    paragraph=False,    # 不合并段落
                    width_ths=0.8,      # 文字宽度阈值
                    height_ths=0.8,     # 文字高度阈值
                    text_threshold=0.7, # 文字检测阈值（提高）
                    low_text=0.4,       # 低文字阈值
                    link_threshold=0.4, # 链接阈值
                    canvas_size=2560,   # 画布大小
                    mag_ratio=1.5       # 放大比例
                )

                # 严格过滤OCR结果
                for bbox, text, conf in raw_ocr_results:
                    text_clean = text.strip()
                    # 更严格的过滤条件：
                    # 1. 置信度 > 0.5 (提高阈值)
                    # 2. 文字长度 >= 2 且 <= 30
                    # 3. 包含有意义的字符
                    # 4. 排除纯符号或乱码
                    if (conf > 0.5 and
                        2 <= len(text_clean) <= 30 and
                        any(c.isalnum() or '\u4e00' <= c <= '\u9fff' for c in text_clean) and
                        not all(c in '.,;:!?()[]{}|-_=+*&^%$#@~`' for c in text_clean)):
                        ocr_results.append((bbox, text_clean, conf))

                print(f"📝 OCR原始检测: {len(raw_ocr_results)} 个，严格过滤后: {len(ocr_results)} 个")
            except Exception as e:
                print(f"⚠️ OCR识别失败: {e}")
        
        # 后处理YOLO检测结果
        yolo_detections = self.post_process_yolo(outputs['detection'], conf_threshold=0.5)

        return {
            'detection': outputs['detection'],
            'yolo_detections': yolo_detections,      # 后处理的YOLO检测框
            'text_detection': outputs['text_detection'],
            'text_recognition': outputs['text_recognition'],
            'ocr_results': ocr_results,
            'original_image': image
        }

    def post_process_yolo(self, detection_outputs, conf_threshold=0.5, nms_threshold=0.4):
        """YOLO检测结果后处理"""
        all_detections = []

        for scale_idx, detection_out in enumerate(detection_outputs):
            batch_size, num_anchors, grid_h, grid_w, num_outputs = detection_out.shape

            # 获取对应尺度的anchor
            anchors = self.anchors[scale_idx]

            # 创建网格坐标
            grid_x = torch.arange(grid_w).repeat(grid_h, 1).float()
            grid_y = torch.arange(grid_h).repeat(grid_w, 1).t().float()

            for batch_idx in range(batch_size):
                for anchor_idx in range(num_anchors):
                    anchor_w, anchor_h = anchors[anchor_idx]

                    # 提取预测值
                    pred = detection_out[batch_idx, anchor_idx]  # [grid_h, grid_w, num_outputs]

                    # 解析预测值
                    x_offset = torch.sigmoid(pred[:, :, 0])  # x偏移
                    y_offset = torch.sigmoid(pred[:, :, 1])  # y偏移
                    w_scale = pred[:, :, 2]  # 宽度缩放
                    h_scale = pred[:, :, 3]  # 高度缩放
                    confidence = torch.sigmoid(pred[:, :, 4])  # 置信度
                    class_probs = torch.softmax(pred[:, :, 5:], dim=-1)  # 类别概率

                    # 计算实际坐标
                    x_center = (grid_x + x_offset) / grid_w
                    y_center = (grid_y + y_offset) / grid_h
                    width = torch.exp(w_scale) * anchor_w / 640  # 假设输入尺寸640
                    height = torch.exp(h_scale) * anchor_h / 640

                    # 过滤低置信度检测
                    conf_mask = confidence > conf_threshold
                    if conf_mask.sum() > 0:
                        # 提取有效检测
                        valid_x = x_center[conf_mask]
                        valid_y = y_center[conf_mask]
                        valid_w = width[conf_mask]
                        valid_h = height[conf_mask]
                        valid_conf = confidence[conf_mask]
                        valid_class_probs = class_probs[conf_mask]

                        # 获取最高类别概率
                        max_class_probs, class_indices = torch.max(valid_class_probs, dim=-1)
                        final_conf = valid_conf * max_class_probs

                        # 转换为边界框格式 [x1, y1, x2, y2]
                        x1 = valid_x - valid_w / 2
                        y1 = valid_y - valid_h / 2
                        x2 = valid_x + valid_w / 2
                        y2 = valid_y + valid_h / 2

                        # 组合检测结果
                        detections = torch.stack([x1, y1, x2, y2, final_conf, class_indices.float()], dim=-1)
                        all_detections.append(detections)

        # 如果有检测结果，应用NMS
        if all_detections:
            all_detections = torch.cat(all_detections, dim=0)
            # 简化的NMS (这里只是示例，实际应该使用更完整的NMS)
            keep_indices = self.simple_nms(all_detections, nms_threshold)
            final_detections = all_detections[keep_indices]
            return final_detections.cpu().numpy()
        else:
            return np.array([])

    def simple_nms(self, detections, nms_threshold):
        """简化的非极大值抑制"""
        if len(detections) == 0:
            return []

        # 按置信度排序
        _, sorted_indices = torch.sort(detections[:, 4], descending=True)

        keep = []
        while len(sorted_indices) > 0:
            # 保留置信度最高的检测
            current = sorted_indices[0]
            keep.append(current)

            if len(sorted_indices) == 1:
                break

            # 计算IoU
            current_box = detections[current, :4]
            remaining_boxes = detections[sorted_indices[1:], :4]

            # 计算交集
            x1 = torch.max(current_box[0], remaining_boxes[:, 0])
            y1 = torch.max(current_box[1], remaining_boxes[:, 1])
            x2 = torch.min(current_box[2], remaining_boxes[:, 2])
            y2 = torch.min(current_box[3], remaining_boxes[:, 3])

            intersection = torch.clamp(x2 - x1, min=0) * torch.clamp(y2 - y1, min=0)

            # 计算并集
            current_area = (current_box[2] - current_box[0]) * (current_box[3] - current_box[1])
            remaining_areas = (remaining_boxes[:, 2] - remaining_boxes[:, 0]) * (remaining_boxes[:, 3] - remaining_boxes[:, 1])
            union = current_area + remaining_areas - intersection

            # 计算IoU
            iou = intersection / union

            # 保留IoU小于阈值的检测
            low_iou_mask = iou < nms_threshold
            sorted_indices = sorted_indices[1:][low_iou_mask]

        return keep


# 简化的训练和测试函数
def create_model():
    """创建共享主干网络模型"""
    print("🔧 创建共享主干网络模型")
    model = SharedBackboneModel(num_classes=47)
    return model


def visualize_results(results, output_path='shared_backbone_result.jpg'):
    """可视化共享主干网络的预测结果"""
    if not results or 'original_image' not in results:
        print("❌ 无法可视化：缺少图像数据")
        return None

    image = results['original_image'].copy()
    h, w = image.shape[:2]

    # 绘制YOLO检测结果
    if 'yolo_detections' in results and len(results['yolo_detections']) > 0:
        print(f"🎯 绘制 {len(results['yolo_detections'])} 个YOLO检测框")
        for detection in results['yolo_detections']:
            x1, y1, x2, y2, conf, class_id = detection

            # 转换为像素坐标
            x1, x2 = int(x1 * w), int(x2 * w)
            y1, y2 = int(y1 * h), int(y2 * h)

            # 绘制检测框 (红色)
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 0, 255), 2)

            # 绘制标签
            label = f"Class{int(class_id)}: {conf:.2f}"
            cv2.putText(image, label, (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
    else:
        print("⚠️ 未检测到YOLO目标")

    # 绘制OCR文字检测结果
    if results['ocr_results']:
        print(f"📝 绘制 {len(results['ocr_results'])} 个OCR文字区域")
        for bbox, text, conf in results['ocr_results']:
            # 绘制文字边界框 (绿色)
            pts = np.array(bbox, np.int32)
            pts = pts.reshape((-1, 1, 2))
            cv2.polylines(image, [pts], True, (0, 255, 0), 2)

            # 绘制文字内容
            x, y = int(bbox[0][0]), int(bbox[0][1])
            cv2.putText(image, f"{text}({conf:.2f})", (x, y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    else:
        print("⚠️ 未检测到OCR文字")

    # 添加模型架构信息
    cv2.putText(image, "Shared Backbone Multi-Task Model", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
    cv2.putText(image, "YOLO Detection (Red) + OCR (Green)", (10, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

    # 保存结果图像
    cv2.imwrite(output_path, image)
    print(f"📸 结果图像已保存: {output_path}")

    return output_path


def test_model(model, image_path):
    """测试模型"""
    print(f"🧪 测试模型，图像: {image_path}")

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return None

    results = model.predict(image_path)

    # 显示YOLO检测结果
    if results and 'yolo_detections' in results and len(results['yolo_detections']) > 0:
        print(f"🎯 YOLO检测完成，检测到 {len(results['yolo_detections'])} 个目标")
        for i, detection in enumerate(results['yolo_detections']):
            x1, y1, x2, y2, conf, class_id = detection
            print(f"   目标{i+1}: 类别{int(class_id)}, 置信度{conf:.3f}, 位置[{x1:.3f},{y1:.3f},{x2:.3f},{y2:.3f}]")
    else:
        print("⚠️ YOLO未检测到目标")

    # 显示OCR检测结果
    if results and results['ocr_results']:
        print(f"📝 OCR检测完成，检测到 {len(results['ocr_results'])} 个文字区域")
        for i, (bbox, text, conf) in enumerate(results['ocr_results']):
            print(f"   文字{i+1}: '{text}' (置信度: {conf:.3f})")
    else:
        print("⚠️ OCR未检测到文字")

    # 可视化结果
    if results:
        output_path = visualize_results(results)
        if output_path:
            print(f"🖼️ 可视化结果已保存到: {output_path}")

    return results


def main():
    """
    主函数：实现共享主干网络的多任务模型
    架构：输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
           ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
           └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
    """
    print("🎯 共享主干网络多任务模型")
    print("=" * 60)
    print("📋 架构说明:")
    print("   输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)")
    print("   ├── 元器件检测头(YOLO head) -> 预测元器件类别和框")
    print("   └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容")
    print("=" * 60)
    
    # 创建模型
    model = create_model()
    
    # 测试模型
    test_image = 'DaYuanTuZ_0.png'
    if os.path.exists(test_image):
        print(f"\n📍 测试模型")
        results = test_model(model, test_image)
        if results:
            print(f"🎉 测试完成！")
            print(f"   YOLO多尺度检测输出:")
            for i, detection_out in enumerate(results['detection']):
                print(f"     尺度{i+1}: {detection_out.shape}")
            print(f"   YOLO检测框数量: {len(results['yolo_detections'])}")
            print(f"   文字检测输出形状: {results['text_detection'].shape}")
            print(f"   文字识别输出形状: {results['text_recognition'].shape}")
            print(f"   OCR文字区域数量: {len(results['ocr_results'])}")
    else:
        print(f"\n⚠️ 测试图像不存在: {test_image}")
    
    print("\n✅ 共享主干网络多任务模型演示完成！")


if __name__ == "__main__":
    main()
