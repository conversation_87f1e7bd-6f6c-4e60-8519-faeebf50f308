"""
共享主干网络多任务模型
架构：输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
       ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
       └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import cv2
import numpy as np
import easyocr
import os


class SharedBackboneModel(nn.Module):
    """共享主干网络多任务模型"""
    
    def __init__(self, num_classes=47):
        super(SharedBackboneModel, self).__init__()
        
        # 共享主干网络 (Backbone)
        self.backbone = nn.Sequential(
            nn.Conv2d(3, 64, 7, stride=2, padding=3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(3, stride=2, padding=1),
            
            nn.Conv2d(64, 128, 3, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.ReL<PERSON>(inplace=True),
            
            nn.Conv2d(128, 256, 3, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(256, 512, 3, stride=2, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )
        
        # 特征金字塔网络 (Neck)
        self.neck = nn.Sequential(
            nn.Conv2d(512, 256, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Upsample(scale_factor=2, mode='nearest'),
        )
        
        # 元器件检测头 (YOLO head)
        self.detection_head = nn.Sequential(
            nn.Conv2d(256, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, num_classes + 5, 1),  # classes + (x,y,w,h,conf)
        )
        
        # 文字检测头 (OCR detection head)
        self.text_detection_head = nn.Sequential(
            nn.Conv2d(256, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 1, 1),  # 文字/非文字二分类
            nn.Sigmoid()
        )
        
        # 文字识别头 (OCR recognition head)
        self.text_recognition_head = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(256, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, 1000),  # 常用汉字词汇表
        )
        
        # OCR引擎
        try:
            self.ocr_engine = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
            print("✅ OCR引擎初始化成功")
        except Exception as e:
            print(f"⚠️ OCR引擎初始化失败: {e}")
            self.ocr_engine = None
        
        print("✅ 共享主干网络模型初始化完成")

    def forward(self, x):
        """
        前向传播：实现共享主干网络的多任务架构
        输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
           ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
           └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
        """
        # 1. 主干网络特征提取 (Backbone)
        backbone_features = self.backbone(x)
        
        # 2. 特征金字塔网络 (Neck)
        neck_features = self.neck(backbone_features)
        
        # 3. 双分支输出
        # 分支1: 元器件检测头 (YOLO head)
        detection_output = self.detection_head(neck_features)
        
        # 分支2: 文字检测+识别头 (OCR head)
        text_detection_output = self.text_detection_head(neck_features)
        text_recognition_output = self.text_recognition_head(neck_features)
        
        return {
            'detection': detection_output,           # 元器件检测结果
            'text_detection': text_detection_output, # 文字区域检测
            'text_recognition': text_recognition_output  # 文字内容识别
        }

    def predict(self, image_path):
        """预测方法"""
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return None
            
        # 读取和预处理图像
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image_tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
        
        # 调整到标准尺寸
        image_tensor = F.interpolate(image_tensor, size=(640, 640), mode='bilinear', align_corners=False)
        
        # 前向传播
        with torch.no_grad():
            outputs = self.forward(image_tensor)
        
        # 使用OCR引擎进行文字识别
        ocr_results = []
        if self.ocr_engine:
            try:
                ocr_results = self.ocr_engine.readtext(image)
            except Exception as e:
                print(f"⚠️ OCR识别失败: {e}")
        
        return {
            'detection': outputs['detection'],
            'text_detection': outputs['text_detection'],
            'text_recognition': outputs['text_recognition'],
            'ocr_results': ocr_results,
            'original_image': image
        }


# 简化的训练和测试函数
def create_model():
    """创建共享主干网络模型"""
    print("🔧 创建共享主干网络模型")
    model = SharedBackboneModel(num_classes=47)
    return model


def visualize_results(results, output_path='shared_backbone_result.jpg'):
    """可视化共享主干网络的预测结果"""
    if not results or 'original_image' not in results:
        print("❌ 无法可视化：缺少图像数据")
        return None

    image = results['original_image'].copy()

    # 绘制OCR文字检测结果
    if results['ocr_results']:
        for bbox, text, conf in results['ocr_results']:
            # 绘制文字边界框
            pts = np.array(bbox, np.int32)
            pts = pts.reshape((-1, 1, 2))
            cv2.polylines(image, [pts], True, (0, 255, 0), 2)

            # 绘制文字内容
            x, y = int(bbox[0][0]), int(bbox[0][1])
            cv2.putText(image, f"{text}({conf:.2f})", (x, y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    # 添加模型架构信息
    cv2.putText(image, "Shared Backbone Multi-Task Model", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
    cv2.putText(image, "Detection + OCR Heads", (10, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

    # 保存结果图像
    cv2.imwrite(output_path, image)
    print(f"📸 结果图像已保存: {output_path}")

    return output_path


def test_model(model, image_path):
    """测试模型"""
    print(f"🧪 测试模型，图像: {image_path}")

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return None

    results = model.predict(image_path)
    if results and results['ocr_results']:
        print(f"✅ 预测完成，检测到 {len(results['ocr_results'])} 个文字区域")
        for i, (bbox, text, conf) in enumerate(results['ocr_results']):
            print(f"   文字{i+1}: '{text}' (置信度: {conf:.3f})")
    else:
        print("✅ 预测完成，未检测到文字")

    # 可视化结果
    if results:
        output_path = visualize_results(results)
        if output_path:
            print(f"🖼️ 可视化结果已保存到: {output_path}")

    return results


def main():
    """
    主函数：实现共享主干网络的多任务模型
    架构：输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)
           ├── 元器件检测头(YOLO head) -> 预测元器件类别和框
           └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容
    """
    print("🎯 共享主干网络多任务模型")
    print("=" * 60)
    print("📋 架构说明:")
    print("   输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)")
    print("   ├── 元器件检测头(YOLO head) -> 预测元器件类别和框")
    print("   └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容")
    print("=" * 60)
    
    # 创建模型
    model = create_model()
    
    # 测试模型
    test_image = 'DaYuanTuZ_0.png'
    if os.path.exists(test_image):
        print(f"\n📍 测试模型")
        results = test_model(model, test_image)
        if results:
            print(f"🎉 测试完成！")
            print(f"   检测输出形状: {results['detection'].shape}")
            print(f"   文字检测输出形状: {results['text_detection'].shape}")
            print(f"   文字识别输出形状: {results['text_recognition'].shape}")
    else:
        print(f"\n⚠️ 测试图像不存在: {test_image}")
    
    print("\n✅ 共享主干网络多任务模型演示完成！")


if __name__ == "__main__":
    main()
